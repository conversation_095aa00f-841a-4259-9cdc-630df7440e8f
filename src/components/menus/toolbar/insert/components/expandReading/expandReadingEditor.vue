<template>
  <div class="box">
    <umo-editor ref="editorRef" v-bind="options" />
  </div>
</template>
<script setup lang="ts">
const { setContentText } = useStore()
const editorRef = $ref(null)
const options = $ref({
  toolbar: {
    enableSourceEditor: false,
    disableMenuItems: ['expandReading'],
  },
  document: {
    // title: '测试文档',
    content: localStorage.getItem('document.content') ?? '<p>测试文档</p>',
  },
  cdnUrl: 'https://cdn.umodoc.com',
  shareUrl: 'https://dutp.cn',
  file: {
    // allowedMimeTypes: [
    //   'application/pdf',
    //   'image/svg+xml',
    //   'video/mp4',
    //   'audio/*',
    // ],
  },
  assistant: {
    enabled: true,
  },
  user: {
    userId: 'dutpeditor',
    nickName: 'Dutp Editor',
    avatarUrl: 'https://tdesign.gtimg.com/site/avatar.jpg',
  },
  async onSave(content: string, page: number, document: { content: string }) {
    localStorage.setItem('document.content', document.content)
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const success = true
        if (success) {
          setContentText(content)
          resolve('操作成功')
        } else {
          reject(new Error('操作失败'))
        }
      }, 2000)
    })
  },
  async onFileUpload(file: File & { url?: string }) {
    if (!file) {
      throw new Error('没有找到要上传的文件')
    }
    await new Promise((resolve) => setTimeout(resolve, 3000))
    return {
      id: null,
      url: file.url ?? URL.createObjectURL(file),
      name: file.name,
      type: file.type,
      size: file.size,
    }
  },
  async onAssistant() {
    return await Promise.resolve('<p>AI助手测试</p>')
  },
  async onCustomImportWordMethod() {
    return await Promise.resolve({
      value: '<p>测试导入word</p>',
    })
  },
})
</script>
