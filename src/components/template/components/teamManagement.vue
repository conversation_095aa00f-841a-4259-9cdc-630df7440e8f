<template>
  <div class="app-container">
    <t-form
      :data="form"
      ref="formRef"
      :rules="rules"
      label-width="120px"
      @submit="save"
    >
      <div
        class="save-btn"
        v-if="isShowSave && (stepId == 1 || auditState == null || auditState == 3) && publishStatus == 1"
      >
        <t-button
          theme="primary"
          size="large"
          type="submit"
          v-if="
            (
              curUserPermissions.permissions.includes(1) ||
              curUserPermissions.permissions.includes(2) || 
              curUserPermissions.permissions.includes(5) ||
              curUserPermissions.permissions.includes(6)
            )
          "
          >保存</t-button
        >
      </div>

      <div class="form-title">作者</div>
      <div class="form-single">
        <t-form-item label="书稿联系人" name="contact" requiredMark>
          <t-select
            v-model="form.contact"
            placeholder="请选择"
            style="width: 200px"
            clearable
            filterable
            :disabled="
              !(curUserPermissions.permissions.includes(5) ||
              curUserPermissions.permissions.includes(6))
            "
          >
            <t-option
              v-for="item in authorUserList"
              :key="item.userId"
              :label="desensitizationPhone(item)"
              :value="item.userId"
            />
          </t-select>
        </t-form-item>
      </div>

      <div class="form-row">
        <div>
          <t-form-item label="主编" name="editor">
            <t-select
              v-model="form.editor"
              multiple
              :max="20"
              style="width: 200px"
              clearable
              filterable
              :disabled="
                !(curUserPermissions.permissions.includes(5) ||
              curUserPermissions.permissions.includes(6))
              "
            >
              <t-option
                v-for="item in authorUserList"
                :key="item.userId"
                :label="desensitizationPhone(item)"
                :value="item.userId"
              />
            </t-select>
          </t-form-item>
        </div>
        <div>
          <t-form-item label="副主编" name="associateEditor">
            <t-select
              v-model="form.associateEditor"
              placeholder="请选择"
              style="width: 200px"
              multiple
              :max="20"
              filterable
              :disabled="
                !(
              curUserPermissions.permissions.includes(1) ||
              curUserPermissions.permissions.includes(2) || 
              curUserPermissions.permissions.includes(5) ||
              curUserPermissions.permissions.includes(6)
            )
              "
              clearable
            >
              <t-option
                v-for="item in authorUserList"
                :key="item.userId"
                :label="desensitizationPhone(item)"
                :value="item.userId"
              />
            </t-select>
          </t-form-item>
        </div>
      </div>

      <div class="form-single">
        <t-form-item label="参编" name="participateCompilation">
          <t-select
            v-model="form.participateCompilation"
            multiple
            :max="20"
            placeholder="请选择"
            style="width: 200px"
            clearable
            filterable
            :disabled="
              !(
              curUserPermissions.permissions.includes(1) ||
              curUserPermissions.permissions.includes(2) || 
              curUserPermissions.permissions.includes(5) ||
              curUserPermissions.permissions.includes(6)
            )
            "
          >
            <t-option
              v-for="item in authorUserList"
              :key="item.userId"
              :label="desensitizationPhone(item)"
              :value="item.userId"
            />
          </t-select>
        </t-form-item>
      </div>

      <div class="form-title" style="margin-top: 30px" v-if="roleKey == 'editor'">编辑</div>

      <div class="form-row" v-if="roleKey == 'editor'">
        <div>
          <t-form-item label="策划编辑" name="planningEditor">
            <t-select
              v-model="form.planningEditor"
              placeholder="请选择"
              style="width: 200px"
              clearable
              filterable
              :disabled="
                !(curUserPermissions.permissions.includes(5) ||
              curUserPermissions.permissions.includes(6))
              "
            >
              <t-option
                v-for="item in userList"
                :key="item.userId"
                :label="desensitizationPhone(item)"
                :value="item.userId"
              />
            </t-select>
          </t-form-item>
        </div>
        <div>
          <t-form-item label="责任编辑" name="editorInCharge">
            <t-select
              v-model="form.editorInCharge"
              multiple
              :max="20"
              placeholder="请选择"
              style="width: 200px"
              clearable
              filterable
              :disabled="
                !(curUserPermissions.permissions.includes(5) ||
              curUserPermissions.permissions.includes(6))
              "
            >
              <t-option
                v-for="item in userList"
                :key="item.userId"
                :label="desensitizationPhone(item)"
                :value="item.userId"
              />
            </t-select>
          </t-form-item>
        </div>
      </div>
    </t-form>
  </div>
</template>

<script setup>
import { listUserNotPage } from '@/api/system/user'
import { getBookGroup, updateBookGroup } from '@/api/book/bookGroup'
import { MessagePlugin } from 'tdesign-vue-next'
const { proxy } = getCurrentInstance()

const props = defineProps({
  bookId: {
    type: String,
    default: null,
  },
  isShowSave: {
    type: Boolean,
    default: true,
  },
  curUserPermissions: {
    type: Object,
    default: {
      permissions: [],
      isEditor: false,
      isAuthor: false,
      bookId: null,
    },
  },
  publishStatus: {
    type: Number,
    default: 1,
  },
  stepId: {
    type: Number,
    default: 1,
  },
  auditState: {
    type: Number,
    default: null,
  }
})
const { roleKey } = useStore()
const userList = ref([])
const authorUserList = ref([])
const formRef = ref(null)
const isShowSave = ref(true)

// 暂存的编辑角色的书稿联系人
const temEditorContact = ref(null)
// 暂存的编辑角色的主编
const temEditorEditor = ref([])
// 暂存的编辑角色的副主编
const temEditorAssociateEditor = ref([])
// 暂存的编辑角色的参编
const temEditorParticipateCompilation = ref([])
const form = ref({
  contact: null, //书稿联系人
  editor: [], //主编
  associateEditor: [], //副主编
  participateCompilation: [], //参编
  planningEditor: null, //策划编辑
  editorInCharge: [], //责任编辑
  bookId: null,
})

const rules = reactive({
  // contact: [
  //   {
  //     required: true,
  //     message: '请选择书稿联系人',
  //     trigger: 'change',
  //   },
  // ],
  planningEditor: [
    {
      required: true,
      message: '请选择策划编辑',
      trigger: 'change',
    },
  ],
})

//#region 监听器相关

//#endregion

//#region 生命周期相关

//#region  生命周期相关

onMounted(() => {
  getInfo()
  getUserList()
})

//#endregion

//#endregion 获取数据相关

// 获取用户列表
async function getUserList() {
  const res = await listUserNotPage()
  userList.value = res.data || []
  if (roleKey.value == 'editor') {
    authorUserList.value = userList.value
  } else {
    // 不展示编辑角色数据
    authorUserList.value = userList.value.filter((item) => item.roleId == 3)
    const temEditorUserList = userList.value.filter((item) => item.roleId == 4)
    // 书稿联系人
    temEditorContact.value = null
    temEditorUserList.forEach((item) => {
      if (item.userId == form.value.contact) {
        temEditorContact.value = form.value.contact
        form.value.contact = null
      }
    })

    // 主编
    temEditorEditor.value = []
    temEditorUserList.forEach((item) => {
      if (form.value.editor.includes(item.userId)) {
        temEditorEditor.value.push(item.userId)
        form.value.editor = form.value.editor.filter((o) => o != item.userId)
      }
    })

    // 副主编
    temEditorAssociateEditor.value = []
    temEditorUserList.forEach((item) => {
      if (form.value.associateEditor.includes(item.userId)) {
        temEditorAssociateEditor.value.push(item.userId)
        form.value.associateEditor = form.value.associateEditor.filter(
          (o) => o != item.userId
        )
      }
    })

    // 参编
    temEditorParticipateCompilation.value = []
    temEditorUserList.forEach((item) => {
      if (form.value.participateCompilation.includes(item.userId)) {
        temEditorParticipateCompilation.value.push(item.userId)
        form.value.participateCompilation = form.value.participateCompilation.filter(
          (o) => o != item.userId
        )
      }
    })
  }

}

//#endregion

//#region 操作相关

// 脱敏手机号
function desensitizationPhone(item) {
  return item.nickName + ' '+  item.phonenumber.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

// 获取数据
async function getInfo() {
  let res = await getBookGroup(props.bookId)
  form.value = res.data
  form.value.editor = res.data.editor || []
  form.value.associateEditor = res.data.associateEditor || []
  form.value.participateCompilation = res.data.participateCompilation || []
  form.value.editorInCharge = res.data.editorInCharge || []

  

  // getBookGroup(props.bookId).then((res) => {
  //   console.log(res.data);
  // })
}

// 保存
function save({ validateResult, firstError }) {
  if (validateResult === true) {
    const tForm = {}
    if (roleKey.value == 'writer') {
      
      // 书稿联系人
      temEditorContact.value = null
      if (temEditorContact.value) { 
        tForm.contact = temEditorContact.value
      }

      // 主编
      if (temEditorEditor.value.length > 0) {
        tForm.editor = [...new Set([...form.value.editor,...temEditorEditor.value])]
      }

      // 副主编
      if (temEditorAssociateEditor.value.length > 0) {
        tForm.associateEditor = [...new Set([...form.value.associateEditor,...temEditorAssociateEditor.value])]
      }

      // 参编
      if (temEditorParticipateCompilation.value.length > 0) {
        tForm.participateCompilation = [...new Set([...form.value.participateCompilation,...temEditorParticipateCompilation.value])]
      }
      console.log("tForm", tForm)
    }
    if (!form.value.contact && !tForm.contact) {
      MessagePlugin.error('请选择书稿联系人')
      return
    }
    updateBookGroup({
      ...form.value,
      ...tForm,
    }).then((res) => {
      if (res.code === 200) {
        MessagePlugin.success('保存成功')
      } else {
        MessagePlugin.error('保存失败')
      }
    })
  } else {
    MessagePlugin.error('保存失败')
  }

  // proxy.$refs["formRef"].validate((valid) => {
  //   if (valid) {
  //
  //   } else {
  //     console.log("error submit!");
  //   }
  // });
}

//#endregion

//#endregion 暴露事件

//   defineExpose({
//     save,
//     getInfo,
//   });
//#endregion
</script>

<style scoped lang="less">
.form-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: bold;
  color: #666;

  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 16px;
    background-color: #409eff;
    border-radius: 8px;
    margin-right: 8px;
  }
}

.form-single {
}

.form-row {
  display: flex;
  align-items: center;
  margin: 10px 0;
}

.save-btn {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
</style>
