<template>
  <div class="textBookTemplate-bg">
    <div class="textBookTemplate-title">
      <t-input
        v-model="searchValue"
        placeholder="请输入关键词"
        clearable
        class="searchInput"
        @change="handleSearch"
      >
        <template #suffixIcon>
          <search-icon :style="{ cursor: 'pointer' }" />
        </template>
      </t-input>
    </div>

    <div class="textBookTemplate-content">
      <div
        v-for="item in dataList"
        :key="item"
        class="textBookTemplate-content-item"
        @click="handleClick(item)"
      >
        <img :src="item.imgUrl" />
        <div class="textBookTemplate-content-item-title">{{ item.modal }}</div>
      </div>
    </div>

    <!-- <div class="textBookTemplate-content">正在努力的开发中~！</div> -->
  </div>
</template>
<script setup>
import { SearchIcon } from 'tdesign-icons-vue-next'

import { getBookTemplateList } from '@/api/book/book'
const { chapterId } = useStore()
const { getPageTemplate, pageTemplateId, setLocalStorage } = useTemplate()
let dataList = $ref([
  // {
  //   id: 1, //模板id
  //   modal: '模板1', //模板名称
  //   imgUrl: 'http://dutp-test.oss-cn-beijing.aliyuncs.com/1739503094535.png', //模板背景图
  //   headerUrl: 'http://dutp-test.oss-cn-beijing.aliyuncs.com/1739509505405.png', //页头背景图
  //   contentUrl:
  //     'http://dutp-test.oss-cn-beijing.aliyuncs.com/1739503094535.png', //内容背景图
  //   footerUrl: 'http://dutp-test.oss-cn-beijing.aliyuncs.com/1739509567855.png', //页脚背景图
  //   chapterHeaderUrl:
  //     'http://dutp-test.oss-cn-beijing.aliyuncs.com/1739582800602.svg', //章头背景图
  //   chapterFontColor: '#333', //章头颜色
  //   chapterHeaderHeight: 257, //章头高度
  //   jointHeaderUrl:
  //     'http://dutp-test.oss-cn-beijing.aliyuncs.com/1739598253503.png',
  //   jointFontColor: '#ffffff', //节头字体颜色
  //   jointHeight: 45, //节头高度,
  //   orderTemplateBgUrl:
  //     'http://dutp-test.oss-cn-beijing.aliyuncs.com/1739758796401.png', //游戏、教学资源、虚拟仿真、AR、3D、拓展阅读、实训、试卷、作业  背景图
  //   orderTemplateMarginLeft: 50, //游戏、教学资源、虚拟仿真、AR、3D、拓展阅读、实训、试卷、作业  控件名称与标题的间距
  //   orderTemplateColor: '#fff', //游戏、教学资源、虚拟仿真、AR、3D、拓展阅读、实训、试卷、作业   字体颜色
  //   pagesFontColor: '#333', //页码字体颜色
  //   pagesAlign: 'center', //页码对齐方式   left center right
  //   pagesPosition: 'top', //页码位置  top bottom
  // },
  // {
  //   id: 2,
  //   modal: '模板2',
  //   imgUrl: 'http://dutp-test.oss-cn-beijing.aliyuncs.com/1739503094535.png',
  //   headerUrl: 'http://dutp-test.oss-cn-beijing.aliyuncs.com/1739509505405.png',
  //   contentUrl:
  //     'http://dutp-test.oss-cn-beijing.aliyuncs.com/1739503094535.png',
  //   footerUrl: 'http://dutp-test.oss-cn-beijing.aliyuncs.com/1739509567855.png',
  //   templateUrl:
  //     'http://dutp-test.oss-cn-beijing.aliyuncs.com/1739582521744.png',
  //   chapterHeaderUrl:
  //     'http://dutp-test.oss-cn-beijing.aliyuncs.com/1739582800602.svg',
  //   jointHeaderUrl:
  //     'http://dutp-test.oss-cn-beijing.aliyuncs.com/1739598253503.png',
  //   fontColor: '#333',
  // },
])
const handleSearch = (value) => {
  if (value) {
    dataList = dataList.filter((item) => item.modal.includes(value))
  }
}

const handleClick = (item) => {

  //localStorage.setItem('textBookTemplate', JSON.stringify(item))
  setLocalStorage({ ...item, chapterId: chapterId.value })
  //getPageTemplate(chapterId.value)
}

onMounted(() => {
  nextTick(() => {
    getList()
   
  })
})
const getList = () => {
  getBookTemplateList().then((res) => {
    dataList = res.data
  })
}

watch(
  () => pageTemplateId,
  (newValue) => {
   
  },
)
</script>
<style scoped lang="less">
.textBookTemplate-bg {
  .textBookTemplate-title {
    padding: 10px;
    :deep(.umo-input) {
      height: 40px;
      border-radius: 20px;
    }
    .searchInput {
      background-color: #fff;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      border: none;
      font-size: 16px;
      color: #333;
      border-radius: 20px;
      margin-bottom: 10px;
    }
  }

  .textBookTemplate-content {
    padding: 0 10px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    .textBookTemplate-content-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      justify-content: center;
      height: 200px;
      padding: 5px;
      border-radius: 12px;
      margin-bottom: 10px;

      img {
        width: 122px;
        height: 176px;
        border-radius: 12px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      }

      .textBookTemplate-content-item-title {
        padding: 5px 0;
        color: #333;
      }
    }
  }
}
</style>
